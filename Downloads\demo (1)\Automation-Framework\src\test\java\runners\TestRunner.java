package runners;

import drivers.DriverManager;
import org.openqa.selenium.WebDriver;
import utiles.SmartActionUtils;

import org.testng.annotations.*;

public class TestRunner {

    private WebDriver driver;
    private SmartActionUtils utils;
    private ProductValidator validator;
    private String entityType;
    private String state;

    @BeforeClass
    public void setUp() {
        // Ask from console
//        Scanner scanner = new Scanner(System.in);
//        System.out.print("Enter Entity Type (e.g. LLC): ");
//        entityType = scanner.nextLine().trim();
//
//        System.out.print("Enter State (e.g. Alabama): ");
//        state = scanner.nextLine().trim();
        entityType = "LLC";
        state = "Alaska";


        // Init WebDriver
        DriverManager.initDriver();
        driver = DriverManager.getDriver();

        // Init utility classes
        utils = new SmartActionUtils(driver);
        validator = new ProductValidator();
    }

    @Test
    public void fullLLCValidationFlow() throws InterruptedException {
        // Step 1: Login
        utils.performSmartActionWithRetry(5); // email
        utils.performSmartActionWithRetry(6); // password
        utils.performSmartActionWithRetry(7); // login
        utils.performSmartActionWithRetry(11); // some second login if needed

        // Step 2: Navigate to product selection page (optional: depends on app flow)
        // utils.performSmartActionWithRetry(8);
        // utils.performSmartActionWithRetry(9);
        // utils.performSmartActionWithRetry(10);

        // Step 3: Select entity and state

        // Step 4: Validate products
        validator.dynamicOrder(state, entityType);
    }


//    @AfterClass
//    public void tearDown() {
//        DriverManager.quitDriver();
//    }
}
