package pages;

import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.ui.WebDriverWait;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.JavascriptExecutor;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;

/**
 * Page Object class for handling product filtering and product list operations
 */
public class ProductFilterPage {
    private final WebDriver driver;
    private final WebDriverWait wait;

    // Locators
    private final By categoryDropdown = By.xpath("//label[contains(text(),'Category') or contains(text(),'Select Category')]/following::div[contains(@class,'p-dropdown')][1]");
    private final By entityTypeDropdown = By.xpath("//label[contains(text(),'Select Entity Type') or contains(text(),'Entity Type')]/following::div[contains(@class,'p-dropdown')][1]");
    private final By stateDropdown = By.xpath("//label[contains(text(),'Select State') or contains(text(),'State')]/following::div[contains(@class,'p-dropdown')][1]");
    private final By countyDropdown = By.xpath("//label[contains(text(),'Select County') or contains(text(),'County')]/following::div[contains(@class,'p-dropdown')][1]");
    
    // Product list locators - these may need to be adjusted based on actual UI
    private final By productList = By.xpath("//div[contains(@class,'product-list')] | //div[contains(@class,'products')] | //ul[contains(@class,'product')] | //div[@class='product-container']");
    private final By productItems = By.xpath(".//div[contains(@class,'product-item')] | .//li[contains(@class,'product')] | .//div[contains(@class,'product-name')] | .//span[contains(@class,'product')]");
    
    // Alternative product locators
    private final By alternativeProductItems = By.xpath("//div[contains(@class,'card')] | //div[contains(@class,'item')] | //li | //span[contains(text(),'Product')] | //div[contains(text(),'Product')]");

    public ProductFilterPage(WebDriver driver) {
        this.driver = driver;
        this.wait = new WebDriverWait(driver, Duration.ofSeconds(15));
    }

    /**
     * Applies filters on the UI: Category, Entity Type, State, and optionally County
     */
    public void applyFilters(String category, String entityType, String state, String county) {
        try {
            System.out.println("🔄 Applying filters: Category=" + category + ", EntityType=" + entityType + 
                             ", State=" + state + ", County=" + county);

            // Apply Category filter
            if (category != null && !category.isEmpty()) {
                selectFromDropdown(categoryDropdown, category, "Category");
                Thread.sleep(1000); // Wait for UI to update
            }

            // Apply Entity Type filter
            if (entityType != null && !entityType.isEmpty()) {
                selectFromDropdown(entityTypeDropdown, entityType, "Entity Type");
                Thread.sleep(1000); // Wait for UI to update
            }

            // Apply State filter
            if (state != null && !state.isEmpty()) {
                selectFromDropdown(stateDropdown, state, "State");
                Thread.sleep(2000); // Wait for product list to load
            }

            // Apply County filter (optional)
            if (county != null && !county.isEmpty()) {
                try {
                    selectFromDropdown(countyDropdown, county, "County");
                    Thread.sleep(1000); // Wait for UI to update
                } catch (Exception e) {
                    System.out.println("⚠️ County filter not available or failed: " + e.getMessage());
                }
            }

            System.out.println("✅ Filters applied successfully");

        } catch (Exception e) {
            System.err.println("❌ Error applying filters: " + e.getMessage());
            throw new RuntimeException("Failed to apply filters", e);
        }
    }

    /**
     * Selects an option from a dropdown
     */
    private void selectFromDropdown(By dropdownLocator, String optionText, String dropdownName) {
        try {
            // Click dropdown to open
            WebElement dropdown = wait.until(ExpectedConditions.elementToBeClickable(dropdownLocator));
            scrollIntoView(dropdown);
            dropdown.click();
            
            Thread.sleep(500); // Wait for dropdown to open
            
            // Find and click the option
            By optionLocator = By.xpath("//li[contains(text(),'" + optionText + "') or contains(@data-label,'" + optionText + "')]");
            WebElement option = wait.until(ExpectedConditions.elementToBeClickable(optionLocator));
            option.click();
            
            System.out.println("✅ Selected '" + optionText + "' from " + dropdownName + " dropdown");
            
        } catch (Exception e) {
            System.err.println("❌ Failed to select '" + optionText + "' from " + dropdownName + " dropdown: " + e.getMessage());
            throw e;
        }
    }

    /**
     * Captures all visible product names from the UI after filters are applied
     */
    public List<String> captureActualProducts() {
        List<String> products = new ArrayList<>();
        
        try {
            System.out.println("🔄 Capturing actual products from UI...");
            
            // Wait for products to load
            Thread.sleep(2000);
            
            // Try multiple strategies to find products
            products = tryMultipleProductCaptureMethods();
            
            if (products.isEmpty()) {
                System.out.println("⚠️ No products found with standard locators, trying alternative methods...");
                products = captureProductsWithAlternativeMethods();
            }
            
            // Clean up product names
            products = cleanProductNames(products);
            
            System.out.println("✅ Captured " + products.size() + " products from UI: " + products);
            
        } catch (Exception e) {
            System.err.println("❌ Error capturing products from UI: " + e.getMessage());
            e.printStackTrace();
        }
        
        return products;
    }

    /**
     * Tries multiple methods to capture products from the UI
     */
    private List<String> tryMultipleProductCaptureMethods() {
        List<String> products = new ArrayList<>();
        
        // Method 1: Look for product container and items within it
        try {
            WebElement productContainer = driver.findElement(productList);
            List<WebElement> items = productContainer.findElements(productItems);
            for (WebElement item : items) {
                String productName = item.getText().trim();
                if (!productName.isEmpty()) {
                    products.add(productName);
                }
            }
            if (!products.isEmpty()) {
                System.out.println("✅ Method 1: Found products in container");
                return products;
            }
        } catch (Exception e) {
            System.out.println("⚠️ Method 1 failed: " + e.getMessage());
        }
        
        // Method 2: Direct search for product items
        try {
            List<WebElement> items = driver.findElements(productItems);
            for (WebElement item : items) {
                String productName = item.getText().trim();
                if (!productName.isEmpty()) {
                    products.add(productName);
                }
            }
            if (!products.isEmpty()) {
                System.out.println("✅ Method 2: Found products directly");
                return products;
            }
        } catch (Exception e) {
            System.out.println("⚠️ Method 2 failed: " + e.getMessage());
        }
        
        return products;
    }

    /**
     * Alternative methods to capture products when standard methods fail
     */
    private List<String> captureProductsWithAlternativeMethods() {
        List<String> products = new ArrayList<>();
        
        // Method 3: Look for any elements that might contain product names
        try {
            List<WebElement> elements = driver.findElements(alternativeProductItems);
            for (WebElement element : elements) {
                String text = element.getText().trim();
                if (!text.isEmpty() && text.length() > 2 && text.length() < 100) {
                    // Filter out common UI elements that are not products
                    if (!isUIElement(text)) {
                        products.add(text);
                    }
                }
            }
            System.out.println("✅ Method 3: Found " + products.size() + " potential products");
        } catch (Exception e) {
            System.out.println("⚠️ Method 3 failed: " + e.getMessage());
        }
        
        // Method 4: Look for specific text patterns that might be products
        try {
            List<WebElement> textElements = driver.findElements(By.xpath("//*[contains(text(),'LLC') or contains(text(),'Corp') or contains(text(),'Formation') or contains(text(),'Service')]"));
            for (WebElement element : textElements) {
                String text = element.getText().trim();
                if (!text.isEmpty() && text.length() > 5 && text.length() < 100) {
                    if (!isUIElement(text) && !products.contains(text)) {
                        products.add(text);
                    }
                }
            }
            System.out.println("✅ Method 4: Found " + products.size() + " text-based products");
        } catch (Exception e) {
            System.out.println("⚠️ Method 4 failed: " + e.getMessage());
        }
        
        return products;
    }

    /**
     * Checks if a text string is likely a UI element rather than a product name
     */
    private boolean isUIElement(String text) {
        String lowerText = text.toLowerCase();
        return lowerText.contains("select") || 
               lowerText.contains("dropdown") || 
               lowerText.contains("button") || 
               lowerText.contains("click") || 
               lowerText.contains("filter") ||
               lowerText.contains("search") ||
               lowerText.equals("category") ||
               lowerText.equals("state") ||
               lowerText.equals("county") ||
               lowerText.equals("entity type");
    }

    /**
     * Cleans up product names by removing extra whitespace and duplicates
     */
    private List<String> cleanProductNames(List<String> products) {
        List<String> cleanedProducts = new ArrayList<>();
        
        for (String product : products) {
            String cleaned = product.trim().replaceAll("\\s+", " ");
            if (!cleaned.isEmpty() && !cleanedProducts.contains(cleaned)) {
                cleanedProducts.add(cleaned);
            }
        }
        
        return cleanedProducts;
    }

    /**
     * Scrolls an element into view
     */
    private void scrollIntoView(WebElement element) {
        ((JavascriptExecutor) driver).executeScript("arguments[0].scrollIntoView(true);", element);
        try {
            Thread.sleep(500);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    /**
     * Takes a screenshot for debugging purposes
     */
    public void takeScreenshot(String fileName) {
        try {
            // Implementation would depend on your screenshot utility
            System.out.println("📸 Screenshot taken: " + fileName);
        } catch (Exception e) {
            System.err.println("❌ Failed to take screenshot: " + e.getMessage());
        }
    }

    /**
     * Waits for the page to load completely
     */
    public void waitForPageLoad() {
        try {
            wait.until(webDriver -> ((JavascriptExecutor) webDriver)
                    .executeScript("return document.readyState").equals("complete"));
            Thread.sleep(1000);
        } catch (Exception e) {
            System.out.println("⚠️ Page load wait interrupted: " + e.getMessage());
        }
    }
}
