package utiles;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;

public class ExcelUtils implements Excel {

    private static Workbook workbook;
    private static Sheet sheet;
    private static String userDirectory = System.getProperty("user.dir");

    private static void openWorkbook(String filePath) {
        String fullPath = userDirectory + "/" + filePath;
        try (FileInputStream inputStream = new FileInputStream(fullPath)) {
            workbook = new XSSFWorkbook(inputStream);
        } catch (IOException e) {
            throw new RuntimeException("Failed to open the Excel workbook: " + e.getMessage(), e);
        }
    }

    public int getRowCount(String filePath, String sheetName) {
        try {
            openWorkbook(filePath);
            sheet = workbook.getSheet(sheetName);
            return sheet.getLastRowNum();
        } finally {
            closeWorkbook();
        }
    }

    public String getCellData(String filePath, String sheetName, int rowNum, int colNum) {
        try {
            openWorkbook(filePath);
            sheet = workbook.getSheet(sheetName);
            if (sheet == null) {
                throw new RuntimeException("Sheet " + sheetName + " does not exist in the workbook.");
            }
            DataFormatter formatter = new DataFormatter();
            return formatter.formatCellValue(sheet.getRow(rowNum - 1).getCell(colNum - 1));
        } finally {
            closeWorkbook();
        }
    }

    public void setCellData(String filePath, String sheetName, int rowNum, int colNum, String value, boolean testPassed) {
        try {
            openWorkbook(filePath);
            sheet = workbook.getSheet(sheetName);
            Cell cell = sheet.getRow(rowNum - 1).createCell(colNum - 1);
            cell.setCellValue(value);

            CellStyle style = workbook.createCellStyle();
            if (testPassed) {
                style.setFillForegroundColor(IndexedColors.LIGHT_GREEN.getIndex());
                style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            } else {
                style.setFillForegroundColor(IndexedColors.RED.getIndex());
                style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            }
            cell.setCellStyle(style);

            try (FileOutputStream outputStream = new FileOutputStream(filePath)) {
                workbook.write(outputStream);
            }
        } catch (IOException e) {
            throw new RuntimeException("Failed to set cell data: " + e.getMessage(), e);
        } finally {
            closeWorkbook();
        }
    }

    public void setCellData(String filePath, String sheetName, int rowNum, int colNum, String value) {
        try {
            openWorkbook(filePath);
            sheet = workbook.getSheet(sheetName);
            sheet.getRow(rowNum - 1).createCell(colNum - 1).setCellValue(value);

            try (FileOutputStream outputStream = new FileOutputStream(filePath)) {
                workbook.write(outputStream);
            }
        } catch (IOException e) {
            throw new RuntimeException("Failed to set cell data: " + e.getMessage(), e);
        } finally {
            closeWorkbook();
        }
    }

    private static void closeWorkbook() {
        if (workbook != null) {
            try {
                workbook.close();
            } catch (IOException e) {
                throw new RuntimeException("Failed to close the Excel workbook: " + e.getMessage(), e);
            } finally {
                workbook = null; // Ensure that the workbook is null after closing
            }
        }
    }
    public void selectEntityAndState(WebDriver driver, String entityType, String state) throws InterruptedException {
        // Click "Select Entity Type"
        driver.findElement(By.xpath("//label[contains(text(),'Select Entity Type')]/following::div[contains(@class,'p-dropdown')]")).click();
        Thread.sleep(500);
        driver.findElement(By.xpath("//li[contains(text(),'" + entityType + "')]")).click();
        Thread.sleep(500);

        // Click "Select State"
        driver.findElement(By.xpath("//label[contains(text(),'Select State')]/following::div[contains(@class,'p-dropdown')]")).click();
        Thread.sleep(500);
        driver.findElement(By.xpath("//li[contains(text(),'" + state + "')]")).click();
        Thread.sleep(2000); // wait for product list to load
    }

}
