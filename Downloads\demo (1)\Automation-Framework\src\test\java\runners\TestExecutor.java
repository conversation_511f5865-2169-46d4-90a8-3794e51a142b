package runners;

import drivers.DriverManager;

import java.util.Scanner;

public class TestExecutor {
    public static void main(String[] args) throws InterruptedException {
        Scanner scanner = new Scanner(System.in);

        System.out.print("Enter Entity Type (LLC/Corp/etc.): ");
        String entityType = scanner.nextLine().trim();

        System.out.print("Enter State: ");
        String state = scanner.nextLine().trim();

        DriverManager.initDriver();
        ProductValidator validator = new ProductValidator();
        validator.dynamicOrder(state, entityType);
        DriverManager.quitDriver();
    }
}
